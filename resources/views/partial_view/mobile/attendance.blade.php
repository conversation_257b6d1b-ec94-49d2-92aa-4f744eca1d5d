<?php
    use App\Models\MobileDevice;
    use App\Library\Helper\AcademicHelper;
    use App\Models\StudentClassAttendance;
    use App\Library\Enums\CalendarEnum;

    $ah = new AcademicHelper();
    $device = MobileDevice::where('auth_code', $authCode)->first();

    $aData = null;
    if($device){
        $now = new \DateTime( "200 days ago", new \DateTimeZone('Asia/Yangon'));
        $interval = new \DateInterval( 'P1D'); // 1 Day interval
        $period = new \DatePeriod( $now, $interval, 15); // 7 Days

        $counter = 0;
        $dates = array();
        foreach ($period as $dt) {
            array_push($dates, $dt->format( 'Y-m-d'));
        }

        $attendanceData = StudentClassAttendance::leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'students_attendance_class.grade_id')
            ->whereIn('date', $dates)
                                                ->where('student_id', $device->student_id)
                                                ->orderBy('date', 'desc')
                                                ->orderBy('week_time', 'asc')
                                                ->get();

        foreach( $attendanceData as $item) {
            $timestamp = strtotime($item->date);
            $weekday = date("l", $timestamp );

            $aData .=  '<tr>'.
                                    '<td>'.$item->date.'</td>'.
                                    '<td>'.$weekday.'</td>'.
                                    '<td>'.$item->grade_name.'</td>'.
                                    '<td>'.$item->week_time.'</td>'.
                                    '<td>'.strtoupper($item->attendance_status).'</td>'.
                                '</tr>';
        }
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="refresh" content="7200">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <meta name="_token" content="{!! csrf_token() !!}"/>
    <link rel="icon" type="image/x-icon" href="/theme_v2/assets/img/favicon.ico" />
    <link href="https://fonts.googleapis.com/css?family=Nunito:400,600,700" rel="stylesheet">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" integrity="sha384-DyZ88mC6Up2uqS4h/KRgHuoeGwBcD4Ng9SiP4dIRy0EXTlnuz47vAwmeGwVChigm" crossorigin="anonymous">
    <link href="/theme/dist/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="/theme/dist/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link href="/theme/dist/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css"/>


<table class="table table-striped border gs-7">
    <thead>
        <th>Date</th>
        <th>Weekday</th>
        <th>Subject</th>
        <th>Period</th>
        <th>Status</th>
    </thead>
    <tbody>
        <?php echo $aData; ?>
    </tbody>
</table>

<script src="/theme/dist/assets/plugins/global/plugins.bundle.js"></script>
<script src="/theme/dist/assets/js/scripts.bundle.js"></script>
</body>

</html>
<?php } else {
     echo "Invalid Authentication code!";
} ?>
