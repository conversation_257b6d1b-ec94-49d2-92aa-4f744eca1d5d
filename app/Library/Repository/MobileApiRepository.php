<?php

 namespace App\Library\Repository;
 use App\Library\Helper\AcademicHelper;
 use Illuminate\Support\Facades\DB;
 use Illuminate\Database\Eloquent\ModelNotFoundException;
 use App\Models\User;
 use App\Models\MobileDevice;
 use App\Models\StudentStorage;
 use App\Models\StudentStorageGlobal;
 use App\Models\SchoolBusData;
 use App\Models\GpsTrack;
 use App\Models\StudentPickupRequest;
 use Illuminate\Support\Str;
 use App\Models\Branch;
 use App\Library\Helper\GeneralHelper;
 use App\Models\Assessment;
 use App\Models\FormativeAssessmentData;
 use App\Models\SummativeAssessmentData;
 use Carbon\Carbon;
 use App\Models\StudentInformation;
 use App\Models\ElectiveGradeStudent;
 use App\Models\Timetable;
 use App\Models\StudentClassAttendance;
 use App\Models\DisciplineRecord;
 use App\Models\DetentionRecord;
 use App\Models\HomeworkDetail;

 class MobileApiRepository {
    protected $ah;

    public function __construct() {
        $this->ah = new AcademicHelper();
    }

    public function studentFiles($authCode) {
        $fileData = null;
        $device = MobileDevice::where('auth_code', $authCode)->first();
        if($device) {
            $userInfo = User::find($device->student_id);
            $globalFiles = StudentStorageGlobal::where('branch_id', $userInfo->branch_id)->get();
            $studentFiles = StudentStorage::where('student_id', $device->student_id)->get();
            $counter = 1;
            foreach ($studentFiles as $key => $fl) {
                $fileData .= $fl->file_name."|*|https://sis.bfi.edu.mm/".$fl->file_url."/*/";
            }
                  foreach ($globalFiles as $key => $fl) {
                $fileData .= $fl->file_name."|*|https://sis.bfi.edu.mm/".$fl->file_url."/*/";
            }
        }
        return $fileData;
    }

    public function checkDeviceData($data) {
        // $ids = explode("/", $data['authCodes']);
        // $allDeviceData = MobileDevice::where('device_token', $data['deviceToken'])
        //                              ->get();
        //
        // foreach ($allDeviceData as $key => $token) {
        //     $isLogged = 0;
        //     foreach ($ids as $id) {
        //         if($id == $token->student_id) {
        //             $isLogged++;
        //         }
        //     }
        //
        //     if($isLogged == 0) {
        //         $deleteData = MobileDevice::destroy('$token->mobile_device_id');
        //     }
        // }
    }

    public function removeUserFromDevice($data) {
        return MobileDevice::where('device_token', $data['deviceToken'])
                    ->where('student_id', $data['userId'])
                    ->delete();
    }

    public function getStudentTracking($authCode) {
        $rd = null;
        $device = MobileDevice::where('auth_code', $authCode)->first();
        if($device) {
            $trackData = SchoolBusData::where('student_id', $device->student_id)->first();
            if($trackData) {
                $lon = $trackData->home_longtitude;
                $lat = $trackData->home_latitude;

                $busTrackData = GpsTrack::where('id_device', $trackData->schoolbus_id)
                                        ->orderBy('id_track', 'desc')
                                        ->first();

                if($busTrackData) {
                    $rd = 'ok|'.$lat.'|'.$lon.'|'.$busTrackData->latitude.'|'.$busTrackData->longitude;
                } else {
                    $rd = 'fail|';
                }
            } else {
                $rd = 'fail|';
            }
        } else {
            $rd = 'fail|';
        }
        return $rd;
    }

    public function mobileNotifications($userIDS) {
        $arr = explode("|", $userIDS);

        $notifications = \DB::table('api_notifications')
                        ->select('notification_body', 'created_at', 'notification_title')
                        ->whereIn('user_id', $arr)
                        // ->orWhere('notification_type', 'all')
                        // ->groupBy('notification_uid')
                        ->groupBy('notification_body', 'created_at', 'notification_title')
                        ->orderBy('created_at', 'desc')
                        ->take(50)
                        ->get();

        $myObj = new \stdClass;
        $jsonString = null;
        $counter = 0;
        foreach ($notifications as $key => $notification) {
            $notificationTimeArr = explode(" ", $notification->created_at);
            $myObj->notificationTitle = $notification->notification_title;
            $myObj->notificationBody = $notification->notification_body;
            $myObj->notificationDate = $notificationTimeArr[0];
            $myObj->notificationTime = $notificationTimeArr[1];
            $myObj->notificationType = null;
            $myJson = json_encode($myObj);
            $jsonString = $jsonString.$myJson;
            $counter++;
        }

        if($counter == 0) {
            $myObj->notificationTitle = "No new notifications";
            $myObj->notificationBody = "We could not find any new notifications.";
            $myObj->notificationDate = " ";
            $myObj->notificationTime = " ";
            $myObj->notificationType = "all";
            $myJson = json_encode($myObj);
            $jsonString = $jsonString.$myJson;
        }

        $jsonString = str_replace("}{","},{",$jsonString);
        $jsonString = "[".$jsonString."]";
        return $jsonString;
    }

    public function createPickupRequest($data) {
        $rd = '';

        $carbonDate = Carbon::now();

        $gh = new GeneralHelper();

        $device = MobileDevice::where('auth_code', $data['authCode'])->first();
        if($device) {

          $userInfo = User::find($device->student_id);
          $branchInfo = Branch::Find($userInfo->branch_id);

          $branchLocation = $branchInfo->gps_location;
          $branchLocArr = explode(",", $branchLocation);

          $distance = $gh->getGpsDistance($branchLocArr[0], $branchLocArr[1], $data['lat'], $data['lon']);

          if($distance <= 150) {

            StudentPickupRequest::firstOrCreate(
              [
                'request_date'    => $carbonDate->format('Y-m-d'),
                'student_id'      => $device->student_id,
                'branch_id'       => $device->student->branch_id,
              ],
              [
                'request_status'  => 0,
                'uuid'              => Str::uuid(),
                'parent_distance'   => $distance." m"
              ]
          );
            $rd = 'ok| Your request recorded for student: ['.$device->student->name.']';
          } else {
            $rd = 'fail|You are too far from campus. Please make request when you are at least 150 meters close to campus! ['.$distance.'m]';
          }
        } else {
            $rd = 'fail|Invalid Auth Code!';
        }
        return $rd;
    }

    public function getStudentTimetable($authCode) {
        $ah = new AcademicHelper();
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        $branch = StudentInformation::where("id", $device->student_id)->first();

        if (!$branch) {
            return response()->json(['error' => 'Student information not found'], 404);
        }

        $courses = ElectiveGradeStudent::select('grade_id')
            ->distinct('grade_id')
            ->where('student_id', $device->student_id)
            ->get();

        $grades = [];
        foreach ($courses as $course) {
            $grades[] = $course->grade_id;
        }

        if (empty($grades)) {
            return response()->json(['error' => 'No courses found for student'], 404);
        }

        $timetable = Timetable::with(['user:id,name', 'subject:subject_id,subject_name'])
            ->where('academic_year_id', $ah->branchAcademicYear($branch->branch_id))
            ->where('branch_id', $device->student->branch_id)
            ->whereIn('grade_id', $grades)
            ->get()
            ->groupBy('week_day');

        return response()->json($timetable);
    }

    public function getStudentAttendanceData($authCode) {
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        // Get the last 15 days of attendance data
        $now = new \DateTime("15 days ago", new \DateTimeZone('Asia/Yangon'));
        $interval = new \DateInterval('P1D'); // 1 Day interval
        $period = new \DatePeriod($now, $interval, 15); // 15 Days

        $dates = array();
        foreach ($period as $dt) {
            array_push($dates, $dt->format('Y-m-d'));
        }

        $attendanceData = StudentClassAttendance::leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'students_attendance_class.grade_id')
            ->whereIn('date', $dates)
            ->where('student_id', $device->student_id)
            ->orderBy('date', 'desc')
            ->orderBy('week_time', 'asc')
            ->get();

        $formattedData = [];
        foreach ($attendanceData as $item) {
            $timestamp = strtotime($item->date);
            $weekday = date("l", $timestamp);

            $formattedData[] = [
                'date' => $item->date,
                'weekday' => $weekday,
                'subject' => $item->grade_name,
                'period' => $item->week_time,
                'status' => strtoupper($item->attendance_status),
                'attendance_note' => $item->attendance_note ?? null
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $formattedData,
            'total_records' => count($formattedData)
        ]);
    }

    public function getStudentBpsData($authCode) {
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        $branch = StudentInformation::where("id", $device->student_id)->first();

        if (!$branch) {
            return response()->json(['error' => 'Student information not found'], 404);
        }

        $records = DisciplineRecord::leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'item_id')
            ->leftJoin('users', 'users.id', 'discipline_bps_records.user_id')
            ->where('student_id', $device->student_id)
            ->where('academic_year_id', $this->ah->branchAcademicYear($branch->branch_id))
            ->where('discipline_bps_records.status', 1)
            ->orderBy('date', 'desc')
            ->select(
                'discipline_bps_records.*',
                'discipline_bps_items.item_title',
                'discipline_bps_items.item_point',
                'users.name as teacher_name'
            )
            ->get();

        $formattedData = [];
        foreach ($records as $record) {
            $formattedData[] = [
                'id' => $record->discipline_record_id,
                'item_title' => $record->item_title,
                'item_type' => strtoupper($record->item_type),
                'item_point' => $record->item_point,
                'date' => $record->date,
                'note' => $record->note ?? '',
                'teacher_name' => $record->teacher_name ?? 'N/A',
                'status' => $record->status
            ];
        }

        // Get detention records for the student
        $detentionRecords = DetentionRecord::leftJoin('discipline_bps_records', 'detention_records.dps_record_id', 'discipline_bps_records.discipline_record_id')
            ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
            ->leftJoin('users', 'users.id', 'discipline_bps_records.user_id')
            ->where('detention_records.student_id', $device->student_id)
            ->where('detention_records.academic_year_id', $this->ah->branchAcademicYear($branch->branch_id))
            ->orderBy('detention_records.date', 'desc')
            ->select(
                'detention_records.*',
                'discipline_bps_items.item_title',
                'discipline_bps_items.item_point',
                'users.name as teacher_name'
            )
            ->get();

        $formattedDetentionData = [];
        foreach ($detentionRecords as $detention) {
            $formattedDetentionData[] = [
                'id' => $detention->detention_record_id,
                'type' => 'DETENTION',
                'detention_type' => $detention->detention_type,
                'served_detention_type' => $detention->served_detention_type,
                'is_served' => $detention->is_served,
                'system_note' => $detention->system_note,
                'item_title' => $detention->item_title ?? 'Detention Record',
                'item_point' => $detention->item_point ?? 0,
                'latest_point' => $detention->latest_point,
                'date' => $detention->date,
                'teacher_name' => $detention->teacher_name ?? 'System',
                'academic_semester' => $detention->academic_semester
            ];
        }

        // Calculate total points
        $totalPoints = $records->sum('item_point');

        return response()->json([
            'success' => true,
            'bps_records' => $formattedData,
            'detention_records' => $formattedDetentionData,
            'total_bps_records' => count($formattedData),
            'total_detention_records' => count($formattedDetentionData),
            'total_points' => $totalPoints
        ]);
    }

    public function getStudentHomeworkData($authCode) {
        $ah = new AcademicHelper();
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        $branch = StudentInformation::where("id", $device->student_id)->first();

        if (!$branch) {
            return response()->json(['error' => 'Student information not found'], 404);
        }

        $homeworks = HomeworkDetail::leftJoin('academic_homework', 'academic_homework.homework_id', 'academic_homework_detail.homework_id')
            ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_homework.grade_id')
            ->leftJoin('subjects', 'subjects.subject_id', 'academic_homework.subject_id')
            ->leftJoin('users', 'users.id', 'academic_homework.user_id')
            ->where('academic_homework.academic_year_id', $ah->branchAcademicYear($branch->branch_id))
            ->where('academic_homework.branch_id', $branch->branch_id)
            ->where('academic_homework_detail.student_id', $device->student_id)
            ->orderBy('academic_homework.homework_id', 'desc')
            ->select(
                'academic_homework_detail.*',
                'academic_homework.title',
                'academic_homework.homework_data',
                'academic_homework.homework_files',
                'academic_homework.homework_video_links',
                'academic_homework.deadline',
                'academic_homework.homework_uid',
                'academic_elective_grade.grade_name',
                'subjects.subject_name',
                'users.name as teacher_name'
            )
            ->get();

        $formattedData = [];
        foreach ($homeworks as $homework) {
            $formattedData[] = [
                'id' => $homework->detail_id,
                'homework_id' => $homework->homework_id,
                'homework_uid' => $homework->homework_uid,
                'title' => $homework->title,
                'subject' => $homework->subject_name ?? 'N/A',
                'grade' => $homework->grade_name ?? 'N/A',
                'teacher_name' => $homework->teacher_name ?? 'N/A',
                'homework_data' => $homework->homework_data,
                'homework_files' => $homework->homework_files,
                'homework_video_links' => $homework->homework_video_links,
                'deadline' => $homework->deadline,
                'viewed_at' => $homework->viewed_at,
                'is_completed' => $homework->is_completed ?? 0,
                'submitted_date' => $homework->sumitted_date,
                'reply_data' => $homework->reply_data,
                'reply_file' => $homework->reply_file,
                'teacher_comment' => $homework->teacher_comment,
                'uuid' => $homework->uuid
            ];
        }

        // Calculate completion statistics
        $totalHomeworks = count($formattedData);
        $completedHomeworks = collect($formattedData)->where('is_completed', 1)->count();
        $pendingHomeworks = $totalHomeworks - $completedHomeworks;

        return response()->json([
            'success' => true,
            'data' => $formattedData,
            'total_records' => $totalHomeworks,
            'completed_count' => $completedHomeworks,
            'pending_count' => $pendingHomeworks,
            'completion_percentage' => $totalHomeworks > 0 ? round(($completedHomeworks / $totalHomeworks) * 100, 1) : 0
        ]);
    }

    public function getStudentGrades($authCode) {
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        $branch = StudentInformation::where("id", $device->student_id)->first();

        if (!$branch) {
            return response()->json(['error' => 'Student information not found'], 404);
        }

        $userId = $device->student_id;
        $academicYearId = $this->ah->branchAcademicYear($branch->branch_id);

        if (!$academicYearId) {
            return response()->json(['error' => 'Academic year not found for this branch'], 404);
        }

        $summativeList = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                                            ->leftJoin('subjects', 'subjects.subject_id', 'academic_summative_assessments.subject_id')
                                            ->leftJoin('users', 'users.id', 'academic_summative_assessments.teacher_id')
                                            ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
                                            ->where('academic_summative_assessments.academic_year_id', $academicYearId)
                                            ->where('academic_summative_assessments_data.score', '>=', 0)
                                            ->where('academic_summative_assessments_data.student_id', $userId)
                                            ->orderBy('academic_summative_assessments.created_at', 'desc')
                                            ->select(
                                                'academic_summative_assessments.assessment_id',
                                                'academic_summative_assessments.date',
                                                'subjects.subject_name',
                                                'academic_summative_assessments.assessment_name',
                                                'academic_summative_assessments_data.type_title',
                                                'academic_summative_assessments_data.score',
                                                'academic_summative_assessments_data.score_percentage',
                                                'academic_summative_assessments.strand as strand_id',
                                                'skills_strands.value as strand_name',
                                                'users.name as teacher_name'
                                            )
                                            ->get();

        $formativeList = FormativeAssessmentData::select(
                                                        'academic_formative_assessments.formative_assessment_id as assessment_id',
                                                        'academic_formative_assessments_data.t1 as tt1',
                                                        'academic_formative_assessments_data.t2 as tt2',
                                                        'academic_formative_assessments_data.t3 as tt3',
                                                        'academic_formative_assessments_data.t4 as tt4',
                                                        'users.name as teacher_name',
                                                        'subjects.subject_name',
                                                        'academic_formative_assessments.assessment_name',
                                                        'academic_formative_assessments.strand as strand_id',
                                                        'strand_info.value as strand_name',
                                                        'academic_formative_assessments.skill as skill_id',
                                                        'skill_info.value as skill_name',
                                                        'academic_formative_assessments.date'
                                                    )
                                            ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
                                            ->leftJoin('subjects', 'subjects.subject_id', 'academic_formative_assessments.subject_id')
                                            ->leftJoin('users', 'users.id', 'academic_formative_assessments.teacher_id')
                                            ->leftJoin('skills_strands as strand_info', 'strand_info.skill_strand_id', 'academic_formative_assessments.strand')
                                            ->leftJoin('skills_strands as skill_info', 'skill_info.skill_strand_id', 'academic_formative_assessments.skill')
                                            ->where('academic_formative_assessments.academic_year_id', $academicYearId)
                                            ->where('academic_formative_assessments_data.student_id', $userId)
                                            ->orderBy('academic_formative_assessments.created_at', 'desc')
                                            ->get();

        return response()->json([
            'summative' => $summativeList,
            'formative' => $formativeList
        ]);
    }
 }

