<?php

use App\Models\User;

use App\Library\Repository\MobileApiRepository;

Route::get('/mobile-api/check-student-credentials/', '\App\Http\Controllers\MobileApiController@checkStudentCredentials');

Route::get('/mobile-api/check-staff-credentials/', '\App\Http\Controllers\MobileApiController@checkStaffCredentials');

Route::get('/mobile-api/get-notifications/', '\App\Http\Controllers\MobileApiController@getNotifications');

Route::get('/mobile-api/messages/', function () {
    return view('partial_view.mobile.messages.main', ['authCode' => $_GET['authCode']]);
});

//route to get student timetable in view
Route::get('/mobile-api/get-student-timetable/', function () {
    return view('partial_view.mobile.timetable', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-timetable2/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentTimetable($_GET['authCode']);
});

Route::get('/mobile-api/get-student-attendance/', function () {
    return view('partial_view.mobile.attendance', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-attendance-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentAttendanceData($_GET['authCode']);
});

Route::get('/mobile-api/get-student-bps/', function () {
    return view('partial_view.mobile.bps', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-bps-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentBpsData($_GET['authCode']);
});

Route::get('/mobile-api/homework/detail/', function () {
    return view('partial_view.student.homework.detail', ['uuid' => $_GET['uuid'], 'stdid' => $_GET['stdid']]);
});

Route::get('/mobile-api/get-student-library/', function () {
    return view('partial_view.mobile.library', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-assessment/', function () {
    return view('partial_view.mobile.assessment', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-health/', function () {
    return view('partial_view.mobile.health', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-documents/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->studentFiles($_GET['authCode']);
});

Route::get('/mobile-api/get-notifications/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->mobileNotifications($_GET['username']);
});

Route::get('/mobile-api/get-student-homework/', function () {
    return view('partial_view.mobile.homework', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-homework-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentHomeworkData($_GET['authCode']);
});

Route::get('/mobile-api/check-device-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->checkDeviceData([
        'authCodes'     => $_GET['authCodes'],
        'deviceToken'   => $_GET['deviceToken']
    ]);
});

Route::get('/mobile-api/remove-user-from-device/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->removeUserFromDevice([
        'userId'        => $_GET['userId'],
        'deviceToken'   => $_GET['deviceToken']
    ]);
});

Route::get('/mobile-api/get-student-tracking/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentTracking($_GET['authCode']);
});

Route::get('/mobile-api/pickup-request/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->createPickupRequest([
        'authCode'  => $_GET['authCode'],
        'lat'       => $_GET['lat'],
        'lon'       => $_GET['lon']
    ]);
});

Route::get('/mobile-api/get-notifications/', function () {
    return view('partial_view.mobile.notification', ['username' => $_GET['username']]);
});

Route::get('/mobile-api/get-teacher-timetable/', function () {
    return view('partial_view.mobile.staff_timetable', ['authCode' => $_GET['authCode']]);
});
Route::post('/mobile-api/attendance/api-store', '\App\Http\Controllers\MobileApiController@storeApiClassAttendance');
//Route::post('/mobile-api/get-teacher-timetable/','\App\Http\Controllers\TimetableController@getDashboardTimetable');

Route::get('/mobile-api/get-teacher-bps/', function () {
    return view('partial_view.mobile.teacher-bps', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-teacher-assessments/', function () {
    return view('partial_view.mobile.teacher-assessment', ['authCode' => $_GET['authCode']]);
});
Route::post('/mobile-api/discipline/store-bps', '\App\Http\Controllers\MobileApiController@storeBps');
Route::post('/mobile-api/discipline/delete-bps', '\App\Http\Controllers\MobileApiController@deleteBps');

// New Teacher API endpoints for JSON data
Route::get('/mobile-api/get-teacher-timetable-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getTeacherTimetableData($_GET['authCode']);
});

Route::get('/mobile-api/get-teacher-bps-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getTeacherBpsData($_GET['authCode']);
});

Route::get('/mobile-api/timetable-attendance/show', function () {
    return view('partial_view.mobile.timetable_attendance', ['timetableId' => $_GET['timetableId']]);
});
//new routes for new mobile app
Route::get('/mobile-api/about/', function () {
    // return view('partial_view.mobile.about_info', ['authCode' => $_GET['authCode']]);
    return view('partial_view.mobile.about_info');
});

Route::get('/mobile-api/contacts/', function () {
    // return view('partial_view.mobile.contacts', ['authCode' => $_GET['authCode']]);
    return view('partial_view.mobile.contacts');
});

Route::get('/mobile-api/finance-info/', function () {
    return view('partial_view.mobile.finance-info', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/faq/', function () {
    return view('partial_view.mobile.faq');
});

Route::get('/mobile-api/news/', function () {
    return view('partial_view.mobile.news', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/calendar/', function () {
    return view('partial_view.mobile.calendar');
});

Route::get('/mobile-api/gallery/', function () {
    return view('partial_view.mobile.gallery', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-grades/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentGrades($_GET['authCode']);
});
